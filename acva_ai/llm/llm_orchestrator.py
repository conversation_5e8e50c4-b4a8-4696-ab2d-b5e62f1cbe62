import asyncio
import logging
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union

from acva_ai.llm.llm_providers.azure_call import call_llm_async as call_azure_async
from acva_ai.llm.llm_providers.lazarus_call import call_lazarus_async
from acva_ai.llm.llm_providers.openai_call import call_openai_async
from acva_ai.utils.usage import ResponseUsage
from acva_ai._params import DEFAULT_LLM_PROVIDER

logger = logging.getLogger(__name__)


class LLMProvider(str, Enum):
    """Enum for supported LLM providers."""

    AZURE = "azure"
    OPENAI = "openai"
    LAZARUS = "lazarus"


class LLMOrchestrator:
    """
    Orchestrates calls to different LLM providers with fallback capabilities.
    """

    def __init__(
        self,
        primary_provider: LLMProvider = LLMProvider(DEFAULT_LLM_PROVIDER),
        max_retries: int = 5,
        retry_delay: float = 10,
    ):
        """
        Initialize the LLM orchestrator.

        Args:
            primary_provider: The primary LLM provider to use
            fallback_providers: List of fallback providers in order of preference
            max_retries: Maximum number of retries per provider
            retry_delay: Initial delay before retrying (will increase exponentially)
        """
        self.primary_provider = primary_provider
        self.fallback_providers = self._get_default_fallbacks(primary_provider)
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _get_default_fallbacks(self, primary: LLMProvider) -> List[LLMProvider]:
        """
        Get default fallback providers based on the primary provider.

        Args:
            primary: The primary provider

        Returns:
            List of fallback providers
        """
        all_providers = [LLMProvider.LAZARUS, LLMProvider.OPENAI, LLMProvider.AZURE]
        return [p for p in all_providers if p != primary]

    async def _call_provider(
        self,
        provider: LLMProvider,
        prompt: Union[str, List[Dict], List[str]],
        model_id: str,
        max_tokens: int,
        use_cache: bool,
        response_usage: Optional[ResponseUsage],
        temperature: float,
    ) -> Tuple[bool, Optional[Dict], Optional[Exception]]:
        """
        Call a specific LLM provider with error handling.

        Args:
            provider: The LLM provider to use
            prompt: The prompt to send
            model_id: The model ID to use
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            temperature: Temperature parameter for the model

        Returns:
            Tuple of (success, response, error)
        """
        try:
            # Use match statement to handle different providers
            match provider:
                case LLMProvider.AZURE:
                    # Azure expects a string prompt
                    azure_prompt = prompt if isinstance(prompt, str) else str(prompt)
                    response = await call_azure_async(
                        prompt=azure_prompt,
                        model_id=model_id,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        max_retries=self.max_retries,
                        retry_delay=self.retry_delay,
                        temperature=temperature,
                    )
                    return True, response, None

                case LLMProvider.OPENAI:
                    # OpenAI can handle both string and message list
                    response = await call_openai_async(
                        # TODO Codrin: Type not matching
                        prompt=prompt,
                        model_id=model_id,
                        max_tokens=max_tokens,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        max_retries=self.max_retries,
                        retry_delay=self.retry_delay,
                        temperature=temperature,
                    )
                    return True, response, None

                case LLMProvider.LAZARUS:
                    # Lazarus can handle both string and list of strings
                    response = await call_lazarus_async(
                        # TODO Codrin: Type not matching
                        prompt=prompt,
                        model_id=model_id,
                        use_cache=use_cache,
                        response_usage=response_usage,
                        max_retries=self.max_retries,
                        retry_delay=self.retry_delay,
                        custom_data=None,
                        webhook_url=None,
                    )
                    # Extract the response dictionary from Lazarus response string
                    return True, response, None

                case _:
                    return False, None, ValueError(f"Unsupported provider: {provider}")

        except Exception as e:
            logger.error(f"Error calling {provider} provider: {str(e)}")
            return False, None, e

    async def call_llm(
        self,
        prompt: Union[str, List[Dict], List[str]],
        # TODO Codrin: Provider should not be sent here, it should only be set at orchestrator initialization
        provider: Optional[LLMProvider] = None,
        model_id: Optional[str] = None,
        max_tokens: int = 1000,
        use_cache: bool = False,
        response_usage: Optional[ResponseUsage] = None,
        temperature: float = 0,
    ) -> Dict:
        """
        Call an LLM with fallback capabilities.

        Args:
            prompt: The prompt to send
            provider: The LLM provider to use (defaults to primary_provider)
            model_id: The model ID to use (provider-specific)
            max_tokens: Maximum tokens to generate
            use_cache: Whether to use cached responses
            response_usage: Optional ResponseUsage object to track costs
            temperature: Temperature parameter for the model

        Returns:
            The model's response as a dictionary

        Raises:
            Exception: If all providers fail
        """
        # Use the specified provider or fall back to the primary provider
        current_provider = provider or self.primary_provider

        # Set default model IDs based on provider
        if model_id is None:
            model_id = self._get_default_model_id(current_provider)

        # Try the primary provider first
        success, response, error = await self._call_provider(
            provider=current_provider,
            prompt=prompt,
            model_id=model_id,
            max_tokens=max_tokens,
            use_cache=use_cache,
            response_usage=response_usage,
            temperature=temperature,
        )

        if success:
            return response

        # If primary provider fails, try fallbacks
        logger.warning(
            f"Primary provider {current_provider} failed with error: {error}. Trying fallbacks."
        )

        # Only try fallbacks if we're using the primary provider
        if provider is None or provider == self.primary_provider:
            for fallback_provider in self.fallback_providers:
                logger.info(f"Trying fallback provider: {fallback_provider}")

                # Get appropriate model ID for the fallback provider
                fallback_model_id = self._get_default_model_id(fallback_provider)

                success, response, error = await self._call_provider(
                    provider=fallback_provider,
                    prompt=prompt,
                    model_id=fallback_model_id,
                    max_tokens=max_tokens,
                    use_cache=use_cache,
                    response_usage=response_usage,
                    temperature=temperature,
                )

                if success:
                    logger.info(
                        f"Successfully used fallback provider: {fallback_provider}"
                    )
                    return response

                logger.warning(
                    f"Fallback provider {fallback_provider} also failed: {error}"
                )

        # If we get here, all providers failed
        error_msg = f"All LLM providers failed. Last error: {error}"
        logger.error(error_msg)
        raise Exception(error_msg)

    def _get_default_model_id(self, provider: LLMProvider) -> str:
        """
        Get the default model ID for a provider.

        Args:
            provider: The LLM provider

        Returns:
            Default model ID for the provider
        """
        match provider:
            case LLMProvider.AZURE:
                from acva_ai._params import OPENAI_MODEL_ID

                return OPENAI_MODEL_ID

            case LLMProvider.OPENAI:
                # TODO Codrin: This should be a environment variable
                return "gpt-4o"

            case LLMProvider.LAZARUS:
                # TODO Codrin: This should be a environment variable
                return "riky2"

            case _:
                raise ValueError(f"Unsupported provider: {provider}")


# TODO Codrin: How about move this to a special directory of test files? This way we can try checking the performance of the different providers.
async def test():
    """Test the LLM orchestrator."""
    response_usage = ResponseUsage()

    # Create orchestrator with Azure as primary and OpenAI, Lazarus as fallbacks
    orchestrator = LLMOrchestrator(
        primary_provider=LLMProvider.OPENAI,
        fallback_providers=[LLMProvider.AZURE, LLMProvider.LAZARUS],
    )

    # Test with a simple prompt
    result = await orchestrator.call_llm(
        prompt="What is the capital of France?",
        use_cache=False,
        response_usage=response_usage,
    )
    print("Result from orchestrator:")
    print(result)
    print(f"Usage: {response_usage}")

    # Test with explicit provider selection
    result_lazarus = await orchestrator.call_llm(
        prompt="What is the capital of Italy?",
        provider=LLMProvider.LAZARUS,
        use_cache=False,
        response_usage=response_usage,
    )
    print("\nResult from explicit Lazarus provider:")
    print(result_lazarus)
    print(f"Usage: {response_usage}")

    # Test with message list for OpenAI
    messages = [
        {"role": "system", "content": "You are a helpful assistant."},
        {"role": "user", "content": "What is the capital of Germany?"},
    ]
    result_messages = await orchestrator.call_llm(
        prompt=messages,
        provider=LLMProvider.OPENAI,
        use_cache=False,
        response_usage=response_usage,
    )
    print("\nResult from OpenAI with message list:")
    print(result_messages)
    print(f"Usage: {response_usage}")


if __name__ == "__main__":
    asyncio.run(test())

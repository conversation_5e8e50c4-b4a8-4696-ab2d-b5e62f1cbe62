import asyncio
import hashlib
import json
import logging
import os
import re
from typing import Dict, List, Optional, Union

import aiohttp
from lazarus_ai import <PERSON><PERSON><PERSON>

from acva_ai._params import CACHE_DIR, LAZARUS_AUTHKEY, LAZARUS_ORGID
from acva_ai.utils.usage import LLMUsage, ResponseUsage

logger = logging.getLogger(__name__)

# Set up cache directory
LAZARUS_CACHE_DIR = os.path.join(CACHE_DIR, "lazarus")
os.makedirs(LAZARUS_CACHE_DIR, exist_ok=True)


def _generate_lazarus_cache_filename(
    prompt: Union[str, List[str]], model_id: str
) -> str:
    """
    Generate a stable filename for the given prompt parameters.

    Args:
        prompt: The prompt or list of prompts
        model_id: The Lazarus model ID

    Returns:
        Path to the cache file
    """
    # Convert list to string if needed
    prompt_str = json.dumps(prompt) if isinstance(prompt, list) else prompt

    # Create a unique hash based on the prompt and model
    prompt_hash = hashlib.md5(prompt_str.encode("utf-8")).hexdigest()
    cache_filename = f"{model_id}_{prompt_hash}.json"
    return os.path.join(LAZARUS_CACHE_DIR, cache_filename)


async def call_lazarus_async(
    prompt: Union[str, List[str]],
    model_id: str = "riky2",
    use_cache: bool = False,
    response_usage: Optional[ResponseUsage] = None,
    current_retry: int = 0,
    max_retries: int = 5,
    retry_delay: float = 10,
    custom_data: Optional[Dict] = None,
    webhook_url: Optional[str] = None,
) -> Dict:
    """
    Asynchronous call to Lazarus AI API with caching.

    Args:
        prompt: The prompt or list of prompts to send to the model
        model_id: The Lazarus model ID (default: "riky2")
        use_cache: Whether to use cached responses
        response_usage: Optional ResponseUsage object to track costs
        max_retries: Maximum number of retries for errors
        retry_delay: Initial delay before retrying (will increase exponentially)
        custom_data: Optional custom JSON to include in the response
        webhook_url: Optional webhook URL for asynchronous processing

    Returns:
        The model's response as a dictionary
    """
    task_id = None
    # Try to extract task ID from the current context if it's in the prompt
    if isinstance(prompt, str):
        task_id_match = re.search(r"\[Task ([a-f0-9-]+)\]", prompt)
        if task_id_match:
            task_id = task_id_match.group(1)

    log_prefix = f"[Task {task_id}] " if task_id else ""

    # Check cache first
    cache_filepath = _generate_lazarus_cache_filename(prompt, model_id)
    if os.path.isfile(cache_filepath) and use_cache:
        try:
            with open(cache_filepath, "r", encoding="utf-8") as f:
                cached_response = json.load(f)
            logger.info(f"{log_prefix}Loaded Lazarus response from cache.")
            return cached_response
        except Exception as e:
            logger.warning(f"{log_prefix}Error reading cache file: {e}")

    # Initialize Lazarus authentication
    auth = LazarusAuth(LAZARUS_ORGID, LAZARUS_AUTHKEY)

    # Use the chat endpoint directly since the lazarus-ai package doesn't support chat
    url = f"https://api.lazarusai.com/api/rikai/chat/{model_id}"
    headers = auth.headers  # Use the headers from the auth object

    # Prepare the data payload
    data = {"question": prompt if isinstance(prompt, list) else [prompt]}

    # Add webhook if provided
    if webhook_url:
        data["webhook"] = webhook_url

    # Add custom data if provided
    if custom_data:
        data["custom"] = custom_data

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=data) as response:
                # Check content type before attempting to parse JSON
                content_type = response.headers.get("Content-Type", "")

                if "application/json" in content_type:
                    response_json = await response.json()
                else:
                    # Handle non-JSON responses
                    response_text = await response.text()

                    # Check if the response text might be the direct answer
                    if (
                        response_text
                        and len(response_text) > 0
                        and not response_text.startswith("{")
                    ):
                        logger.info(
                            f"{log_prefix}Received plain text response, returning as is"
                        )

                        # Create a response structure similar to JSON format
                        response_json = {
                            "status": "SUCCESS",
                            "message": "Plain text response",
                            "data": [{"answer": response_text}],
                        }

                        # Calculate token usage (approximate)
                        input_tokens = (
                            len(prompt)
                            if isinstance(prompt, str)
                            else sum(len(p) for p in prompt)
                        )
                        output_tokens = len(response_text)

                        # Record usage
                        llm_usage = LLMUsage(
                            model_id=model_id,
                            cost=0,  # TODO: Update when pricing is available
                            input_tokens=input_tokens,
                            output_tokens=output_tokens,
                        )
                        if response_usage:
                            response_usage.add_llm_usage(llm_usage)

                        # Cache the response
                        try:
                            with open(cache_filepath, "w", encoding="utf-8") as f:
                                json.dump(
                                    response_json, f, ensure_ascii=False, indent=2
                                )
                        except Exception as e:
                            logger.warning(
                                f"{log_prefix}Error writing to cache file: {e}"
                            )

                        return response_text
                    else:
                        logger.warning(
                            f"{log_prefix}Received non-JSON response: {response_text[:100]}..."
                        )

                        # If we're not at max retries, try again
                        if current_retry < max_retries:
                            wait_time = retry_delay * (2**current_retry)
                            logger.warning(
                                f"{log_prefix}Non-JSON response received. Retrying in {wait_time} seconds (attempt {current_retry + 1}/{max_retries})"
                            )
                            await asyncio.sleep(wait_time)
                            return await call_lazarus_async(
                                prompt=prompt,
                                model_id=model_id,
                                use_cache=use_cache,
                                response_usage=response_usage,
                                current_retry=current_retry + 1,
                                max_retries=max_retries,
                                retry_delay=retry_delay,
                                custom_data=custom_data,
                                webhook_url=webhook_url,
                            )
                        else:
                            # Create a fallback response structure
                            response_json = {
                                "status": "FAILURE",
                                "message": f"Non-JSON response: {response_text[:100]}...",
                                "data": [{"answer": response_text}],
                            }

                # Handle API errors
                if response.status != 200 or response_json.get("status") == "FAILURE":
                    error_msg = (
                        f"API error: {response_json.get('message', 'Unknown error')}"
                    )
                    logger.error(f"{log_prefix}{error_msg}")

                    # Handle retries for certain errors
                    if current_retry < max_retries:
                        wait_time = retry_delay * (2**current_retry)
                        logger.warning(
                            f"{log_prefix}Retrying in {wait_time} seconds (attempt {current_retry + 1}/{max_retries})"
                        )
                        await asyncio.sleep(wait_time)
                        return await call_lazarus_async(
                            prompt=prompt,
                            model_id=model_id,
                            use_cache=use_cache,
                            response_usage=response_usage,
                            current_retry=current_retry + 1,
                            max_retries=max_retries,
                            retry_delay=retry_delay,
                            custom_data=custom_data,
                            webhook_url=webhook_url,
                        )
                    else:
                        raise Exception(error_msg)

                # Extract the answer from the response
                answer = response_json["data"][0]["answer"]

                # Calculate token usage (approximate)
                input_tokens = (
                    len(prompt)
                    if isinstance(prompt, str)
                    else sum(len(p) for p in prompt)
                )
                output_tokens = len(answer)

                # Record usage
                cost = 0  # TODO: Update when pricing is available
                llm_usage = LLMUsage(
                    model_id=model_id,
                    cost=cost,
                    input_tokens=input_tokens,
                    output_tokens=output_tokens,
                )
                if response_usage:
                    response_usage.add_llm_usage(llm_usage)

                # Cache the response
                try:
                    with open(cache_filepath, "w", encoding="utf-8") as f:
                        json.dump(response_json, f, ensure_ascii=False, indent=2)
                except Exception as e:
                    logger.warning(f"{log_prefix}Error writing to cache file: {e}")

                logger.info(f"{log_prefix}Lazarus response: {answer[:100]}...")
                return answer

    except aiohttp.ClientError as e:
        logger.error(f"{log_prefix}Connection error: {str(e)}")

        if current_retry < max_retries:
            wait_time = retry_delay * (2**current_retry)
            logger.warning(
                f"{log_prefix}Retrying in {wait_time} seconds (attempt {current_retry + 1}/{max_retries})"
            )
            await asyncio.sleep(wait_time)
            return await call_lazarus_async(
                prompt=prompt,
                model_id=model_id,
                use_cache=use_cache,
                response_usage=response_usage,
                current_retry=current_retry + 1,
                max_retries=max_retries,
                retry_delay=retry_delay,
                custom_data=custom_data,
                webhook_url=webhook_url,
            )
        else:
            raise


def test():
    """Test the Lazarus API call function."""
    response_usage = ResponseUsage()

    try:
        # Test with a simple prompt
        result = asyncio.run(
            call_lazarus_async(
                prompt="What is the capital of France?",
                response_usage=response_usage,
                use_cache=False,  # Disable caching
            )
        )
        print("API Response:")
        print(result)
        print(f"Usage: {response_usage}")

        # Test with a list of prompts
        result_list = asyncio.run(
            call_lazarus_async(
                prompt=[
                    "What is the capital of France?",
                    "What is the capital of Italy?",
                ],
                response_usage=response_usage,
                use_cache=True,  # Enable caching
            )
        )
        print("\nList Prompt Response:")
        print(result_list)
        print(f"Usage: {response_usage}")

    except Exception as e:
        print(f"Error during test: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    test()
